<mujoco model="table_arena">
  <asset>
    <texture builtin="gradient" height="256" rgb1=".9 .9 1." rgb2=".2 .3 .4" type="skybox" width="256"/>

    <!-- floor texture and material-->

    <texture file="../textures/tile_grigia_caldera_porcelain_floor.png" type="2d" name="texplane"/>
    
    <material name="floorplane" reflectance="0.0" shininess="0.0"
	      specular="0.0" texrepeat="3 3" texture="texplane" texuniform="true"/>

    <!-- table texture and material -->
    <texture file="../textures/martin_novak_wood_table.png" type="cube" name="tex-table"/>
    <material name="table_texture" reflectance="0.0" shininess="0.0" specular="0.2" texrepeat="1 1" texture="tex-table" />
    <!-- table legs texture and material -->
    <texture file="../textures/martin_novak_wood_table.png" type="cube" name="tex-table-legs"/>
    <material name="table_legs" reflectance="0.8" shininess="0.8" texrepeat="1 1" texture="tex-table-legs" />

    <!-- wall texture and material -->
    <texture file="../textures/smooth_light_gray_plaster.png" type="2d" name="tex-wall"/>
    <material name="walls_mat" reflectance="0.0" shininess="0.3" specular="0.8" texrepeat="3 3" texture="tex-wall" texuniform="true" />

    <!-- added table texture and material for domain randomization -->
    <texture  name="textable" builtin="flat" height="512" width="512" rgb1="0.8 0.8 0.8" rgb2="0.8 0.8 0.8"/>
    <material name="table_mat" texture="textable" />

    <!-- Specify your own scene texture, change it on your own. -->
    <texture file="kitchen_background/kitchen_background.png" name="tex-custom" type="2d"/>
    <material name="custom" reflectance="0.5" texrepeat="1 1" texture="tex-custom" texuniform="false"/>
    <mesh file="kitchen_background/visual/kitchen_background_vis.msh" name="custom_vis" scale="0.01 0.01 0.01"/>

  </asset>
  <worldbody>
    <!-- Floor -->
    <geom condim="3" group="1" material="floorplane" name="floor" pos="0 0 0" size="3 3 .125" type="plane"/>

    <geom pos="-1.65 0.0 0.0" quat="0 0 0 1" type="mesh" mesh="custom_vis" conaffinity="0" contype="0" group="1" material="custom"/>
    
    <light name="light_kitchen" diffuse=".8 .8 .8" dir="0 0 -1" directional="true" pos="-1.75 0 1.6" specular="0.8 0.8 0.8" castshadow="false"/>

    <geom pos="0.5 3 1.5" quat="0.7071 0.7071 0 0" size="2.5 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_left_visual" material="walls_mat"/>
    <geom pos="0.5 -3 1.5" quat="0.7071 -0.7071 0 0" size="2.5 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_right_visual" material="walls_mat"/>
    <geom pos="-2 0 1.5" quat="0.5 0.5 0.5 0.5" size="3 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_rear_visual" material="walls_mat"/>
    <geom pos="3 0 1.5" quat="0.5 0.5 -0.5 -0.5" size="3 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_front_visual" material="walls_mat"/>
    <!-- Table body -->
    <body name="table" pos="0 0 0.4">
      <geom pos="0 0 0" size="0.4 0.4 0.4" type="box" group="0" name="table_collision" friction="1 0.005 0.0001"/>
      <geom pos="0 0 0" size="0.4 0.4 0.4" type="box" conaffinity="0" contype="0" group="1" name="table_visual" material="table_texture"/>
      <site pos="0 0 0.4" name="table_top" size="0.001 0.001 0.001" rgba="0 0 0 0"/>
      <!-- Legs (visual only) -->
      <geom pos="0 0 0" size="0.05 0.1" type="cylinder" conaffinity="0" contype="0" group="1" name="table_leg1_visual" material="table_legs"/>
      <geom pos="0 0 0" size="0.05 0.1" type="cylinder" conaffinity="0" contype="0" group="1" name="table_leg2_visual" material="table_legs"/>
      <geom pos="0 0 0" size="0.05 0.1" type="cylinder" conaffinity="0" contype="0" group="1" name="table_leg3_visual" material="table_legs"/>
      <geom pos="0 0 0" size="0.05 0.1" type="cylinder" conaffinity="0" contype="0" group="1" name="table_leg4_visual" material="table_legs"/>
    </body>

    <light name="light1" diffuse=".8 .8 .8" dir="0 -.15 -1" directional="false" pos="1 1 4.0" specular="0.3 0.3 0.3" castshadow="false"/>
    <light name="light2" diffuse=".8 .8 .8" dir="0 -.15 -1" directional="false" pos="-3. -3. 4.0" specular="0.3 0.3 0.3" castshadow="false"/>

    <!-- front view -->
    <camera mode="fixed" name="frontview" pos="1.0 0 1.45" quat="0.56 0.43 0.43 0.56"/>
    <!-- bird view -->
    <camera mode="fixed" name="birdview" pos="-0.2 0 3.0" quat="0.7071 0 0 0.7071"/>
    <!-- agent view -->
    <camera mode="fixed" name="agentview" pos="0.5 0 1.35" quat="0.653 0.271 0.271 0.653"/>

    <!-- side view -->
    <camera mode="fixed" name="sideview" pos="-0.05651774593317116 1.2761224129427358 1.4879572214102434" quat="0.009905065491771751 0.006877963156909582 0.5912228352893879 0.806418094001364" />
  </worldbody>
</mujoco>

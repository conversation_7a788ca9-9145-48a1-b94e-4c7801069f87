<mujoco model="living_room_arena">
  <asset>
    <texture builtin="gradient" height="256" rgb1=".9 .9 1." rgb2=".2 .3 .4" type="skybox" width="256"/>
    <texture file="../textures/tile_grigia_caldera_porcelain_floor.png" type="2d" name="texplane"/>    
    <material name="floorplane" reflectance="0.0" shininess="0.0"
	      specular="0.0" texrepeat="3 3" texture="texplane" texuniform="true"/>
    <!-- ceramic table texture and material-->
    <texture file="../textures/seamless_wood_planks_floor.png" type="cube" name="tex-table"/>
    <material name="table_texture" reflectance="0.0" shininess="0.0" specular="0.2" texrepeat="1 1" texture="tex-table" />
    <!-- steel legs -->
    <texture file="../textures/seamless_wood_planks_floor.png" type="cube" name="tex-table-legs"/>
    <material name="table_legs" reflectance="0.8" shininess="0.8" texrepeat="1 1" texture="tex-table-legs" />
    <!-- plaster walls -->
    <texture file="../textures/smooth_light_gray_plaster.png" type="2d" name="tex-wall"/>

    <material name="walls_mat" reflectance="0.0" shininess="0.2" specular="0.9" texrepeat="3 3" texture="tex-wall" texuniform="true" />
    <!-- added table texture and material for domain randomization -->
    <texture  name="textable" builtin="flat" height="512" width="512" rgb1="0.5 0.5 0.5" rgb2="0.5 0.5 0.5"/>
    <material name="table_mat" texture="textable" />

    <texture file="living_room_table/living_room_table_texture.png" name="tex-living_room_table" type="2d"/>
    <material name="living_room_table" reflectance="0.5" texrepeat="1 1" texture="tex-living_room_table" texuniform="false"/>
    <mesh file="living_room_table/visual/living_room_table_vis.msh" name="living_room_table_vis" scale="1.5 1.5 1.5"/>
    
    <texture file="wall_decoration/wall_decoration_texture.png" name="tex-wall_decoration" type="2d"/>
    <material name="wall_decoration" reflectance="0.5" texrepeat="1 1" texture="tex-wall_decoration" texuniform="false"/>
    <mesh file="wall_decoration/visual/wall_decoration_vis.msh" name="wall_decoration_vis" scale="0.4 0.1 0.4"/>

    <texture file="living_room/living_room_wall_unit.png" name="tex-living_room" type="2d"/>
    <material name="living_room" reflectance="0.5" texrepeat="1 1" texture="tex-living_room" texuniform="false"/>
    <mesh file="living_room/visual/living_room_vis.msh" name="living_room_vis" scale="0.7 0.7 0.7"/>

  </asset>
  <worldbody>
    <!-- Floor -->
    <body name="floor" pos="0 0 0">    
      <geom condim="3" group="1" material="floorplane" name="floor" pos="0 0 0" size="3 3 .125" type="plane"/>
    </body>

    <body name="living_room_table" pos="0 0 0" quat="1 0.0 0.0 0.0">    
    <body name="living_room_table_col" pos="-0.25 0.25 0" quat="0.707 0.0 0.0 0.707">    
    <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="mesh" mesh="living_room_table_vis" conaffinity="0" contype="0" group="1" material="living_room_table"/>
    <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="-0.19761 0.00000 0.42492" quat="0.70711 0.00000 0.70711 0.00000" size="0.01184 0.44432 0.67171" group="0" rgba="0.8 0.8 0.8 0.3"/>
    <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="0.54936 -0.00000 0.42492" quat="0.50000 0.50000 0.50000 -0.50000" size="0.01184 0.10133 0.41675" group="0" rgba="0.8 0.8 0.8 0.3"/>
    <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="-0.97006 0.00000 0.42492" quat="0.50000 -0.50000 -0.50000 -0.50000" size="0.01184 0.10133 0.41675" group="0" rgba="0.8 0.8 0.8 0.3"/>
    <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="-0.19761 -0.51121 0.42492" quat="0.70711 0.00000 0.70711 0.00000" size="0.01184 0.07752 0.67171" group="0" rgba="0.8 0.8 0.8 0.3"/>
    <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="-0.19761 0.49665 0.42492" quat="0.70711 0.00000 0.70711 0.00000" size="0.01184 0.07752 0.67171" group="0" rgba="0.8 0.8 0.8 0.3"/>
    <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="-0.00000 0.00000 0.20414" quat="0.70711 -0.00000 -0.70711 -0.00000" size="0.20884 0.41162 0.43516" group="0" rgba="0.8 0.8 0.8 0.3"/>
    </body>
    </body>
    <geom pos="-1.95 0.60 1.5" quat="0.7071068 0 0 -0.7071068" type="mesh" mesh="wall_decoration_vis" conaffinity="0" contype="0" group="1" name="wall_decoration" material="wall_decoration"/>
    <geom pos="-1.7 0.0 0" quat="0 0 0 1" type="mesh" mesh="living_room_vis" conaffinity="0" contype="0" group="1" name="living_room" material="living_room"/>
    
    <geom pos="-1.25 2.25 1.5" quat="0.6532815 0.6532815 0.2705981 0.2705981" size="1.06 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_leftcorner_visual" material="walls_mat"/>
    <geom pos="-1.25 -2.25 1.5" quat="0.6532815 0.6532815 -0.2705981 -0.2705981" size="1.06 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_rightcorner_visual" material="walls_mat"/>
    <geom pos="1.25 3 1.5" quat="0.7071 0.7071 0 0" size="1.75 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_left_visual" material="walls_mat"/>
    <geom pos="1.25 -3 1.5" quat="0.7071 -0.7071 0 0" size="1.75 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_right_visual" material="walls_mat"/>
    <geom pos="-2 0 1.5" quat="0.5 0.5 0.5 0.5" size="1.5 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_rear_visual" material="walls_mat"/>
    <geom pos="3 0 1.5" quat="0.5 0.5 -0.5 -0.5" size="3 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_front_visual" material="walls_mat"/>
    <!-- Table body -->
    <light name="light1" diffuse=".8 .8 .8" dir="0 -.15 -1" directional="false" pos="1 1 4.0" specular="0.3 0.3 0.3" castshadow="false"/>
    <light name="light2" diffuse=".8 .8 .8" dir="0 -.15 -1" directional="false" pos="-3. -3. 4.0" specular="0.3 0.3 0.3" castshadow="false"/>

    <!-- front view -->
    <camera mode="fixed" name="frontview" pos="1.0 0 1.45" quat="0.56 0.43 0.43 0.56"/>
    <!-- bird view -->
    <camera mode="fixed" name="birdview" pos="-0.2 0 3.0" quat="0.7071 0 0 0.7071"/>
    <!-- agent view -->
    <camera mode="fixed" name="agentview" pos="0.5 0 1.35" quat="0.653 0.271 0.271 0.653"/>

    <!-- side view -->
    <camera mode="fixed" name="sideview" pos="-0.05651774593317116 1.2761224129427358 1.4879572214102434" quat="0.009905065491771751 0.006877963156909582 0.5912228352893879 0.806418094001364" />
  </worldbody>
</mujoco>

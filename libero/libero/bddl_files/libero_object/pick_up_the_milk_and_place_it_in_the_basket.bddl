(define (problem LIBERO_Floor_Manipulation)
  (:domain robosuite)
  (:language Pick the milk and place it in the basket)
    (:regions
      (bin_region
          (:target floor)
          (:ranges (
              (-0.01 0.25 0.01 0.27)
            )
          )
      )
      (target_object_region
          (:target floor)
          (:ranges (
              (-0.145 -0.265 -0.095 -0.215)
            )
          )
      )
      (other_object_region_0
          (:target floor)
          (:ranges (
              (0.025 -0.125 0.07500000000000001 -0.07500000000000001)
            )
          )
      )
      (other_object_region_1
          (:target floor)
          (:ranges (
              (-0.175 0.034999999999999996 -0.125 0.08499999999999999)
            )
          )
      )
      (other_object_region_2
          (:target floor)
          (:ranges (
              (0.07500000000000001 -0.225 0.125 -0.17500000000000002)
            )
          )
      )
      (other_object_region_3
          (:target floor)
          (:ranges (
              (0.125 0.0049999999999999975 0.175 0.055)
            )
          )
      )
      (other_object_region_4
          (:target floor)
          (:ranges (
              (-0.225 -0.10500000000000001 -0.17500000000000002 -0.055)
            )
          )
      )
      (contain_region
          (:target basket_1)
      )
    )

  (:fixtures
    floor - floor
  )

  (:objects
    milk_1 - milk
    basket_1 - basket
    cream_cheese_1 - cream_cheese
    tomato_sauce_1 - tomato_sauce
    butter_1 - butter
    orange_juice_1 - orange_juice
    chocolate_pudding_1 - chocolate_pudding
  )

  (:obj_of_interest
    milk_1
    basket_1
  )

  (:init
    (On milk_1 floor_target_object_region)
    (On cream_cheese_1 floor_other_object_region_0)
    (On tomato_sauce_1 floor_other_object_region_1)
    (On butter_1 floor_other_object_region_2)
    (On orange_juice_1 floor_other_object_region_3)
    (On chocolate_pudding_1 floor_other_object_region_4)
    (On basket_1 floor_bin_region)
  )

  (:goal
    (And (In milk_1 basket_1_contain_region))
  )

)

(define (problem LIBERO_Kitchen_Tabletop_Manipulation)
  (:domain robosuite)
  (:language put the wine bottle in the bottom drawer of the cabinet)
    (:regions
      (white_cabinet_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.01 0.29 0.01 0.31)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (wine_rack_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.11 -0.31 -0.09000000000000001 -0.29)
            )
          )
          (:yaw_rotation (
              (3.141592653589793 3.141592653589793)
            )
          )
      )
      (akita_black_bowl_init_region
          (:target kitchen_table)
          (:ranges (
              (0.0049999999999999975 -0.07500000000000001 0.055 -0.025)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (wine_bottle_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.175 0.025 -0.125 0.07500000000000001)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (top_side
          (:target white_cabinet_1)
      )
      (top_region
          (:target white_cabinet_1)
      )
      (middle_region
          (:target white_cabinet_1)
      )
      (bottom_region
          (:target white_cabinet_1)
      )
      (top_region
          (:target wine_rack_1)
      )
    )

  (:fixtures
    kitchen_table - kitchen_table
    white_cabinet_1 - white_cabinet
    wine_rack_1 - wine_rack
  )

  (:objects
    akita_black_bowl_1 - akita_black_bowl
    wine_bottle_1 - wine_bottle
  )

  (:obj_of_interest
    wine_bottle_1
    white_cabinet_1
  )

  (:init
    (On akita_black_bowl_1 kitchen_table_akita_black_bowl_init_region)
    (On wine_bottle_1 kitchen_table_wine_bottle_init_region)
    (On white_cabinet_1 kitchen_table_white_cabinet_init_region)
    (On wine_rack_1 kitchen_table_wine_rack_init_region)
    (Open white_cabinet_1_bottom_region)
  )

  (:goal
    (And (In wine_bottle_1 white_cabinet_1_bottom_region))
  )

)

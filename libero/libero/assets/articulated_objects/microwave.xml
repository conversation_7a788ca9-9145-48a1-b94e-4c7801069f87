<mujoco model="microwave">

    <compiler inertiafromgeom="auto" inertiagrouprange="4 4" angle="radian"/>
    <asset>
        <mesh file="meshes/microwave/micro.stl" name="micro" scale="0.5 0.5 0.5"/>
        <mesh file="meshes/microwave/microdoor.stl" name="microdoor" scale="0.5 0.5 0.5"/>
        <mesh file="meshes/microwave/microbutton.stl" name="microbutton" scale="0.5 0.5 0.5"/>
        <mesh file="meshes/microwave/microfeet.stl" name="microfeet" scale="0.5 0.5 0.5"/>
        <mesh file="meshes/microwave/microhandle.stl" name="microhandle" scale="0.5 0.5 0.5"/>
        <mesh file="meshes/microwave/microwindow.stl" name="microwindow" scale="0.5 0.5 0.5"/>

        <texture name="T_micro_metal" type="cube" height="1" width="1" file="textures/metal1.png"/>

        <material name="micro_metal" rgba="1 1 1 1" texture="T_micro_metal" texrepeat="3 3" reflectance="1" shininess="1" texuniform="false"/>
        <material name="micro_black" rgba=".2 .2 .2 1" reflectance="1" shininess="1"/>
        <material name="micro_window" rgba=".4 .4 .4 .25" reflectance="1" shininess="1"/>
        <material name="micro_collision_blue" rgba="0.3 0.3 1.0 0.5" shininess="0" specular="0"/>
    </asset>

    <default>
        <default class="microwave">A
            <geom conaffinity="0" contype="0" group="1" material="micro_black" type="mesh"/>
        </default>
        
        <default class="micro_collision">
            <geom conaffinity="1" condim="3" contype="0" group="0" margin="0.001" material="micro_collision_blue" solimp=".8 .9 .01" solref=".02 1"/>
        </default>
    </default>

    <worldbody>
    <body>
        <body name="object">
            <geom class="microwave" mesh="micro"/>
            <geom class="microwave" material="micro_metal" mesh="microbutton"/>
            <geom class="microwave" material="micro_metal" mesh="microfeet"/>
	    <site rgba="1 0 0 0" type="box" pos="0.0 0.01 0.20" quat="1 0 0 0" size="0.12 0.0835 0.005" name="top_side"/>
	    <site rgba="1 0 0 0" type="box" pos="0.0 0.01 0.096" quat="1 0 0 0" size="0.12 0.0835 0.08" name="heating_region"/>
            <geom class="micro_collision" pos="-0.158 0.0115 0.0935" size="0.0145 0.09955 0.0935" type="box" mass=".5"/>
            <geom class="micro_collision" pos="0.118 0.0115 0.0935" size="0.0545 0.09955 0.0935" type="box" mass=".5"/>
            <geom class="micro_collision" pos="-0.0405 0.0955 0.0935" size="0.1035 0.015 0.0935" type="box" mass=".5"/>
            <geom class="micro_collision" pos="-0.04 -0.0035 0.1775" size="0.1035 0.0845 0.0095" type="box" mass=".5"/>
            <geom class="micro_collision" pos="-0.04 -0.004 0.012" size="0.1035 0.084 0.012" type="box" mass=".5"/>
            <geom class="micro_collision" pos="0.13 -0.0985 0.0935" size="0.0425 0.012 0.0935" type="box" mass=".5"/>
            <body name="microdoorroot" pos="-0.1725 -0.088 0.096">
                <joint type="hinge" axis="0 0 1" limited="true"
		       name="microjoint" range="-2.094 0" damping="0.5"/>
                <geom class="microwave" mesh="microdoor" pos="0.1725 0.088 -0.096"/>
                <geom class="microwave" material="micro_window" mesh="microwindow" pos="0.1725 0.088 -0.096"/>
                <geom class="microwave" material="micro_metal" pos="0.2375 -0.054 0" size="0.01 0.065" type="capsule"/>
                <geom class="microwave" material="micro_metal" euler="1.57 0 0" pos="0.2375 -0.0375 0.065" size="0.01 0.015" type="capsule"/>
                <geom class="microwave" material="micro_metal" euler="1.57 0 0" pos="0.2375 -0.0375 -0.065" size="0.01 0.015" type="capsule"/>

                <geom class="micro_collision" pos="0.2375 -0.054 0" size="0.01 0.065" type="capsule" mass="0.020"/>
                <geom class="micro_collision" euler="1.57 0 0" pos="0.2375 -0.0375 .065" size="0.01 0.015" type="capsule" mass="0.020"/>
                <geom class="micro_collision" euler="1.57 0 0" pos="0.2375 -0.0375 -.065" size="0.01 0.015" type="capsule" mass="0.020"/>
                <geom class="micro_collision" pos="0.1295 -0.013 0" size="0.1295 0.012 0.0925" type="box" mass="0.20"/>
            </body>
        </body>
      <site rgba="0 0 0 0" size="0.005" pos="0 0 -0.06" name="bottom_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0 0 0.06" name="top_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0.015 0.015 0" name="horizontal_radius_site" />
    </body>
    </worldbody>

</mujoco>

(define (problem LIBERO_Living_Room_Tabletop_Manipulation)
  (:domain robosuite)
  (:language put the white mug on the plate and put the chocolate pudding to the right of the plate)
    (:regions
      (plate_left_region
          (:target living_room_table)
          (:ranges (
              (0.09999999999999999 -0.15000000000000002 0.2 -0.05)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (plate_right_region
          (:target living_room_table)
          (:ranges (
              (0.09999999999999999 0.05 0.2 0.15000000000000002)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (plate_init_region
          (:target living_room_table)
          (:ranges (
              (0.125 -0.025 0.175 0.025)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (porcelain_mug_init_region
          (:target living_room_table)
          (:ranges (
              (-0.125 -0.175 -0.07500000000000001 -0.125)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (chocolate_pudding_init_region
          (:target living_room_table)
          (:ranges (
              (-0.07500000000000001 0.07500000000000001 -0.025 0.125)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (red_coffee_mug_init_region
          (:target living_room_table)
          (:ranges (
              (-0.225 -0.025 -0.17500000000000002 0.025)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
    )

  (:fixtures
    living_room_table - living_room_table
  )

  (:objects
    porcelain_mug_1 - porcelain_mug
    red_coffee_mug_1 - red_coffee_mug
    plate_1 - plate
    chocolate_pudding_1 - chocolate_pudding
  )

  (:obj_of_interest
    plate_1
    chocolate_pudding_1
    porcelain_mug_1
  )

  (:init
    (On plate_1 living_room_table_plate_init_region)
    (On red_coffee_mug_1 living_room_table_red_coffee_mug_init_region)
    (On chocolate_pudding_1 living_room_table_chocolate_pudding_init_region)
    (On porcelain_mug_1 living_room_table_porcelain_mug_init_region)
  )

  (:goal
    (And (On porcelain_mug_1 plate_1) (On chocolate_pudding_1 living_room_table_plate_right_region))
  )

)

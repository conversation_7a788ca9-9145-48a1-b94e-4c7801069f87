(define (problem LIBERO_Kitchen_Tabletop_Manipulation)
  (:domain robosuite)
  (:language open the top drawer of the cabinet)
    (:regions
      (wooden_cabinet_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.01 -0.31 0.01 -0.29)
            )
          )
          (:yaw_rotation (
              (3.141592653589793 3.141592653589793)
            )
          )
      )
      (akita_black_bowl_middle_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.07500000000000001 0.17500000000000002 -0.025 0.225)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (akita_black_bowl_front_init_region
          (:target kitchen_table)
          (:ranges (
              (0.07500000000000001 0.125 0.125 0.175)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (akita_black_bowl_back_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.175 0.025 -0.125 0.07500000000000001)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (plate_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.025 -0.025 0.025 0.025)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (top_side
          (:target wooden_cabinet_1)
      )
      (top_region
          (:target wooden_cabinet_1)
      )
      (middle_region
          (:target wooden_cabinet_1)
      )
      (bottom_region
          (:target wooden_cabinet_1)
      )
    )

  (:fixtures
    kitchen_table - kitchen_table
    wooden_cabinet_1 - wooden_cabinet
  )

  (:objects
    akita_black_bowl_1 akita_black_bowl_2 akita_black_bowl_3 - akita_black_bowl
    plate_1 - plate
  )

  (:obj_of_interest
    wooden_cabinet_1
  )

  (:init
    (On akita_black_bowl_1 kitchen_table_akita_black_bowl_front_init_region)
    (On akita_black_bowl_2 kitchen_table_akita_black_bowl_middle_init_region)
    (On akita_black_bowl_3 kitchen_table_akita_black_bowl_back_init_region)
    (On plate_1 kitchen_table_plate_init_region)
    (On wooden_cabinet_1 kitchen_table_wooden_cabinet_init_region)
  )

  (:goal
    (And (Open wooden_cabinet_1_top_region))
  )

)

<mujoco model="cream_cheese">
  <asset>
  <texture file="texture_map.png" name="tex-cream_cheese" type="2d" />
  <material name="cream_cheese" reflectance="0.5" texrepeat="1 1" texture="tex-cream_cheese" texuniform="false" />
  <mesh file="visual/cream_cheese_vis.msh" name="cream_cheese_vis" scale="0.008 0.008 0.008" /></asset>
  <worldbody>
    <body>
      <body name="object">
      <!-- <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="mesh" mesh="cream_cheese_vis" conaffinity="0" contype="0" group="1" material="cream_cheese" /><geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="0.00000 0.00000 -0.00000" quat="0.00707 0.00000 0.00707 0.00000" size="0.01117 0.02667 0.05076" group="0" rgba="0.8 0.8 0.8 0.0" /> -->
      <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="mesh" mesh="cream_cheese_vis" conaffinity="0" contype="0" group="1" material="cream_cheese" /><geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="0.00000 0.00000 -0.00000" quat="0.00707 0.00000 0.00707 0.00000" size="0.008936 0.021336 0.040608" group="0" rgba="0.8 0.8 0.8 0.0" />
      </body>
      <site rgba="0 0 0 0" size="0.005" pos="0 0 -0.025" name="bottom_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0 0 0.025" name="top_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0.03 0.03 0" name="horizontal_radius_site" />
    </body>
  </worldbody>
</mujoco>

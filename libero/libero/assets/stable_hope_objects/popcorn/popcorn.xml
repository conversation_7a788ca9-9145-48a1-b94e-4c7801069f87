<mujoco model="popcorn">
  <asset>
  <texture file="texture_map.png" name="tex-popcorn" type="2d" />
  <material name="popcorn" reflectance="0.5" texrepeat="1 1" texture="tex-popcorn" texuniform="false" />
  <mesh file="visual/popcorn_vis.msh" name="popcorn_vis" scale="0.005 0.005 0.005" /></asset>
  <worldbody>
    <body>
      <body name="object">
        <geom pos="0 0 0" mesh="popcorn_vis" type="mesh" solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" material="popcorn" contype="0" conaffinity="0" group="1"/>
	<geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="0.00000 0.00000 -0.00000" quat="0.00707 0.00000 0.00707 0.00000" size="0.01 0.021 0.031" group="0" rgba="0.8 0.8 0.8 1.0" />	
      </body>
      <site rgba="0 0 0 0" size="0.005" pos="0 0 -0.06" name="bottom_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0 0 0.04" name="top_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0.025 0.025 0" name="horizontal_radius_site" />
    </body>
  </worldbody>
</mujoco>
